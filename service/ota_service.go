package service

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/exec"
	"time"
	"worker-ota/config"
	"worker-ota/gateway"
	"worker-ota/internal/util"
	"worker-ota/model"

	"go.uber.org/zap"
)

const (
	otaFolderPath  = "/etc/ota-data"
	otaPayloadDir  = "/data/os"
	scriptFileName = "deploy_ota_script.sh"
	deviceIdFile   = "/home/<USER>/.device_id"
)

type IOtaService interface {
	SyncFirmware(ctx context.Context) error
}

type OtaService struct {
	deviceType model.DeviceType
	cloudGw    gateway.ICloudGateway
	logger     *zap.Logger
	config     config.OtaConfig
}

func NewOtaService(cloudGw gateway.ICloudGateway, deviceType model.DeviceType, logger *zap.Logger, config config.OtaConfig) IOtaService {
	return &OtaService{
		deviceType: deviceType,
		cloudGw:    cloudGw,
		logger:     logger,
		config:     config,
	}
}

func (s *OtaService) SyncFirmware(ctx context.Context) error {
	ipToMacs, err := util.GetIpMacAddress()
	if err != nil {
		s.logger.Error("Error get IP mac Address", zap.Error(err))
		return err
	}
	s.logger.Debug("Network info: ", zap.Any("network_info", ipToMacs))
	internalIpAddress := ipToMacs[0].Ip
	macAddress := ipToMacs[0].Mac

	fmt.Println("internalIpAddress:", internalIpAddress, "macAddress:", macAddress)

	/**
	1. Check if kubeconfig Is Exist
	-> If not, then call cloud API to get init cmd and execute it
	**/
	s.logger.Debug("kubeconfig: " + s.config.PathKubeConfig)
	s.logger.Debug("pathInitCmd: " + s.config.PathInitCmd)
	s.logger.Debug("cloudUrl: " + s.config.CloudUrl)
	s.logger.Debug("deviceType: " + s.config.DeviceType)

	deviceIdFileIsExist := util.FileExists(deviceIdFile)

	if !deviceIdFileIsExist {
		s.logger.Info("Device ID file is not exist")
		initCmd, err := s.cloudGw.GetBaetylInitCmd(ctx, macAddress)
		if err != nil {
			s.logger.Error("Error get baetyl init cmd", zap.Error(err))
			return err
		}
		s.logger.Info("initCmd: " + initCmd)

		if initCmd != "" {
			s.logger.Info("Run init cmd ...")
			err := runInitCMD(initCmd)
			os.Remove(s.config.PathInitCmd)
			if err != nil {
				s.logger.Error("Run init cmd failed", zap.Error(err))
				return err
			}
			s.logger.Info("Init cmd: Done")
		}
	}

	/**
	2. Get current device's infor (Jetpack version, OTA version, device id)
	**/
	jetpackVersion, err := util.GetJetpackVersion()
	if err != nil {
		s.logger.Error("Error get jetpack version", zap.Error(err))
		return err
	}
	s.logger.Debug("Jetpack version: ", zap.Any("jetpack_version", jetpackVersion))

	firmwareVersion, err := util.GetFirmwareVersion()
	if err != nil {
		s.logger.Error("Error get firmware version", zap.Error(err))
		return err
	}
	s.logger.Debug("Firmware version: ", zap.Any("firmware_version", firmwareVersion))

	deviceId, err := util.GetDeviceId()
	if err != nil {
		s.logger.Error("Error get device id", zap.Error(err))
		return err
	}
	s.logger.Debug("Device Id: ", zap.Any("deviceId", deviceId))

	/**
	3. Sync data with cloud
	**/
	s.logger.Info("Sending sync request to Cloud API ...")
	syncVersionInfo, err := s.cloudGw.SyncFirmwareVersion(ctx, gateway.SyncFirmwareVersionInput{
		InternalIp:      internalIpAddress,
		MacAddress:      macAddress,
		JetpackVersion:  jetpackVersion,
		FirmwareVersion: firmwareVersion,
		DeviceId:        deviceId,
		DeviceType:      s.deviceType,
	})
	if err != nil {
		s.logger.Error("Sync firmware version to cloud failed", zap.Error(err))
		return err
	}

	if syncVersionInfo.DownloadUrl != "" && syncVersionInfo.Hash != "" {
		/**
		4. Create folders
		**/
		err = util.CreateFolders(otaFolderPath)
		if err != nil {
			s.logger.Error("Create folder failed", zap.Error(err))
			return err
		}

		err = util.CreateFolders(otaPayloadDir)
		if err != nil {
			s.logger.Error("Create folder {otaPayloadDir} failed", zap.Error(err))
			return err
		}

		/**
		5. Get file name to download
		**/
		fileName, err := util.ExtractFileNameFromUrl(syncVersionInfo.DownloadUrl)
		if err != nil {
			s.logger.Error("Extract filename failed", zap.Error(err))
			return err
		}
		otaPayloadPath := otaPayloadDir + "/" + fileName

		/**
		6. Check if need download
		**/
		fmt.Println(syncVersionInfo.DownloadUrl)
		isDownload, err := s.isDownloadRequire(syncVersionInfo, otaPayloadPath)
		if err != nil {
			s.logger.Error("Check download require failed", zap.Error(err))
			return err
		}

		/**
		7. Download process
		**/
		// Download OTA tool
		targetOtaToolUrl := syncVersionInfo.TargetOtaToolUrl
		s.logger.Info("Dowload OTA tool ... ", zap.Any("targetOtaToolUrl", targetOtaToolUrl))
		err = s.downloadOtaTool(targetOtaToolUrl)
		if err != nil {
			return err
		}

		// Download OTA firmware
		if isDownload {
			s.logger.Info("Downloading new firmware package ...")
			downloadCtx, cancel := context.WithCancel(context.Background())
			doneChan := make(chan bool, 1)
			go func() {
				err := s.downloadFileWithProgress(downloadCtx, syncVersionInfo.DownloadUrl, otaPayloadPath, doneChan)
				if err != nil {
					doneChan <- true
				}
			}()

		loop:
			for {
				// sync status
				status, err := s.syncStatus(ctx, deviceId, macAddress, model.OtaStatusPending)
				if err != nil {
					s.logger.Error("Sync firmware status failed", zap.Error(err))
					cancel()
					return err
				}
				if status == model.OtaStatusCanceled {
					s.logger.Info("Cancel Download!")
					cancel()
					return nil
				}

				// Check if the goroutine has completed
				select {
				case <-doneChan:
					s.logger.Info("Download completed")
					cancel()
					break loop
				default:
					// Continue looping
				}

				time.Sleep(3 * time.Second)
			}
		}

		/**
		8. Check hash key for the file firmware downloaded
		**/
		s.logger.Info("Checking hash key...")
		isMatch, err := util.CompareHashKey(otaPayloadPath, syncVersionInfo.Hash)
		if err != nil {
			s.logger.Error("Get hash file failed", zap.Error(err))
			return err
		}
		if !isMatch {
			s.logger.Error("Hash is different")
			return fmt.Errorf("hash is not the same")
		}
		s.logger.Info("Hash Key Matched!")

		// sync status
		s.logger.Info("Sync status: UPGRADING")
		status, err := s.syncStatus(ctx, deviceId, macAddress, model.OtaStatusUpgrading)
		if err != nil {
			s.logger.Error("Sync firmware status failed", zap.Error(err))
			return err
		}

		if status == model.OtaStatusCanceled ||
			status == model.OtaStatusFailed ||
			status == model.OtaStatusSucceeded {
			s.logger.Info("Stop upgrade!")
			return nil
		}

		// 6. upgrade
		scriptPath := otaFolderPath + "/" + scriptFileName
		s.logger.Info("Upgrading new firmware package ...")
		err = runUpgradeFirmware(scriptPath, otaPayloadPath)
		if err != nil {
			s.logger.Error("Run upgrade firmware failed", zap.Error(err))
			return err
		}
	}

	return nil
}

// Sync up OTA upgrading status with cloud
func (s *OtaService) syncStatus(ctx context.Context, deviceId string, macAddress string, curStatus model.OtaStatus) (model.OtaStatus, error) {
	status, err := s.cloudGw.SyncFirmwareStatus(ctx, gateway.SyncFirmwareStatusInput{
		DeviceId:   deviceId,
		MacAddress: macAddress,
		Status:     curStatus,
	})
	if err != nil {
		s.logger.Error("Sync firmware status failed", zap.Error(err))
		return status, err
	}
	return status, err
}

// Check if need download a file
func (s *OtaService) isDownloadRequire(syncVersionInfo model.SyncFirmwareVersionInfo, filepath string) (bool, error) {
	isFileExist := util.FileExists(filepath)
	if !isFileExist {
		return true, nil
	} else {
		// Check hash key
		isMatch, err := util.CompareHashKey(filepath, syncVersionInfo.Hash)
		if err != nil {
			s.logger.Error("Get hash file failed", zap.Error(err))
			return true, err
		}
		if !isMatch {
			s.logger.Info("Hash is different. Need download new file ...")
			return true, nil
		}
	}
	return false, nil
}

type contextReader struct {
	ctx context.Context
	r   io.Reader
}

func (cr *contextReader) Read(p []byte) (int, error) {
	select {
	case <-cr.ctx.Done():
		return 0, cr.ctx.Err()
	default:
		return cr.r.Read(p)
	}
}

func (s *OtaService) downloadFileWithProgress(ctx context.Context, url string, filepath string, done chan bool) error {
	// Send HTTP GET request
	response, err := http.Get(url)
	if err != nil {
		return fmt.Errorf("failed to download file: %v", err)
	}
	defer response.Body.Close()

	// Create a new file
	outFile, err := os.Create(filepath)
	if err != nil {
		return fmt.Errorf("failed to create file: %v", err)
	}
	defer outFile.Close()

	// Create a progress writer
	progressWriter := &model.ProgressWriter{
		Total:      response.ContentLength,
		AnchorTime: time.Now(),
		Logger:     s.logger,
	}

	ctxReader := &contextReader{ctx, response.Body}
	tee := io.TeeReader(ctxReader, progressWriter)

	// Copy the response body to the file
	_, err = io.Copy(outFile, tee)
	if err != nil {
		return fmt.Errorf("failed to write file: %v", err)
	}

	fmt.Println("Download completed")
	done <- true

	return nil
}

func (s *OtaService) downloadOtaTool(url string) error {
	ota_tool_file_name := "ota_tools_aarch64.tbz2"
	fileStoragePath := fmt.Sprintf("/etc/ota-data/%s", ota_tool_file_name)

	// Check if the file exists and remove
	if util.FileExists(fileStoragePath) {
		err := os.Remove(fileStoragePath)
		if err != nil {
			return err
		}
	}

	// Send a GET request
	resp, err := http.Get(url)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// Check if the response status is OK (200)
	if resp.StatusCode != http.StatusOK {
		s.logger.Error("Download OTA Tool failed", zap.Error(err))
		return err
	}

	// Create the file on the local filesystem
	out, err := os.Create(fileStoragePath)
	if err != nil {
		s.logger.Error("Create local file failed", zap.Error(err))
		return err
	}
	defer out.Close()

	// Copy the data from the response to the file
	_, err = io.Copy(out, resp.Body)
	if err != nil {
		s.logger.Error("Copy OTA Tool failed", zap.Error(err))
		return err
	}

	s.logger.Info("File downloaded successfully")
	return nil
}

func runInitCMD(initCmd string) error {
	cmd := exec.Command("sh", "-c", initCmd)
	return execCommandCommon(cmd)
}

func runUpgradeFirmware(scriptPath string, otaPackpagePath string) error {
	cmd := exec.Command("/bin/bash", scriptPath, otaPackpagePath)
	return execCommandCommon(cmd)
}

func execCommandCommon(cmd *exec.Cmd) error {
	stdoutPipe, err := cmd.StdoutPipe()
	if err != nil {
		return fmt.Errorf("error getting stdout pipe: %v", err)
	}

	// Get the error pipe for the command
	stderrPipe, err := cmd.StderrPipe()
	if err != nil {
		return fmt.Errorf("error getting stderr pipe: %v", err)
	}

	// Start the command
	if err := cmd.Start(); err != nil {
		return fmt.Errorf("error starting command: %v", err)
	}

	// Create a buffered reader for the stdout pipe
	stdoutReader := bufio.NewReader(stdoutPipe)
	stderrReader := bufio.NewReader(stderrPipe)

	// Function to read and print output
	printOutput := func(reader *bufio.Reader, label string) {
		for {
			line, err := reader.ReadString('\n')
			if err != nil {
				if err.Error() != "io: read/write on closed pipe" {
					fmt.Printf("Error reading %s: %s\n", label, err)
				}
				break
			}
			fmt.Printf("[%s] %s", label, line)
		}
	}

	// Start reading stdout and stderr
	go printOutput(stdoutReader, "STDOUT")
	go printOutput(stderrReader, "STDERR")

	// Wait for the command to finish
	if err := cmd.Wait(); err != nil {
		return fmt.Errorf("error waiting for command: %v", err)
	}

	return nil
}
