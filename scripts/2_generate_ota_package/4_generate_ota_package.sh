#!/bin/sh
# Function to print usage
usage() {
    echo "Usage: $0 --base_bsp {ex: 35.5, 36.4} --target_bsp {ex: 35.5, 36.4}"
    exit 1
}

# Check if the correct number of arguments is provided
if [[ "$1" != "--base_bsp" ]]; then
    usage
fi

if [[ "$3" != "--target_bsp" ]]; then
    usage
fi

BASE_BSP=$2
TARGET_BSP=$4
echo "BASE_BSP: $BASE_BSP"
echo "TARGET_BSP: $TARGET_BSP"

if [ $BASE_BSP == "35.5" ]; then
    BASE_BSP_VERSION="R35-5"
    BASE_BSP_FOLDER_NAME=${PWD}/Linux_for_Tegra_35_5/MVE2_OS
elif [ $BASE_BSP == "36.4" ]; then
    BASE_BSP_VERSION="R36-4"
    BASE_BSP_FOLDER_NAME=${PWD}/Linux_for_Tegra_36_4/MVE2_OS
else
    echo "Invalid Base BSP version"
    usage
fi

if [ $TARGET_BSP == "35.5" ]; then
    TARGET_BSP_FOLDER_NAME=${PWD}/Linux_for_Tegra_35_5/MVE2_OS
elif [ $TARGET_BSP == "36.4" ]; then
    TARGET_BSP_FOLDER_NAME=${PWD}/Linux_for_Tegra_36_4/MVE2_OS
else
    echo "Invalid Target BSP version"
    usage
fi

cd $TARGET_BSP_FOLDER_NAME
sudo BASE_BSP=$BASE_BSP_FOLDER_NAME ./tools/ota_tools/version_upgrade/l4t_generate_ota_package.sh jetson-agx-orin-devkit $BASE_BSP_VERSION

# if [ $BASE_BSP == $TARGET_BSP ]; then
#     echo "BASE_BSP and TARGET_BSP are the same"
#     usage
# fi

# VERSION="R35-5"
# if [ $TARGET_BSP == "35.5" ]; then
#     VERSION="R35-5"
#     BSP_FOLDER_NAME="Linux_for_Tegra_35_5"
# elif [ $TARGET_BSP == "36.4" ]; then
#     VERSION="R36-4"
#     BSP_FOLDER_NAME="Linux_for_Tegra_36_4"
# else
#     echo "Invalid BSP version"
#     usage
# fi

# cd $BSP_FOLDER_NAME/MVE2_OS
# sudo ./tools/ota_tools/version_upgrade/l4t_generate_ota_package.sh -s -f mve2.img.raw -r jetson-agx-orin-devkit $VERSION

# sudo BASE_BSP=/data/danny/DIGO_OS/BASE_BSP/Linux_for_Tegra_35_5/MVE2_OS_355/ ./tools/ota_tools/version_upgrade/l4t_generate_ota_package.sh -s -f mve2.img.raw jetson-agx-orin-devkit R35-5
# sudo -E ROOTFS_AB=1 ROOTFS_ENC=1 BASE_BSP=/data/danny/DIGO_OS/BASE_BSP/Linux_for_Tegra_35_5/MVE2_OS_355/ ./tools/ota_tools/version_upgrade/l4t_generate_ota_package.sh -s -f mve2.img.raw jetson-agx-orin-devkit R35-5
