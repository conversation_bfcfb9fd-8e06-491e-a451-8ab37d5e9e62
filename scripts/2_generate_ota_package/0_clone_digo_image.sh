#!/bin/bash

# Function to print usage
usage() {
    echo "Usage: $0 --jetpack {ex: 35.5, 36.4}"
    exit 1
}

# Check if the correct number of arguments is provided
if [[ "$1" != "--jetpack" ]]; then
    usage
fi

JETPACK=$2
echo "JETPACK: $JETPACK"

# Set variables
GIT_USERNAME="agxos"
GIT_PASSWORD="agx%402024"
if [ $JETPACK == "35.5" ]; then
    GIT_REPO="git.digotech.net/tvf_agx_orin/operatingsystem/os.git"
    BRANCH="v105"
    ZIPPED_IMAGE="MVE2_OS_V1.0.5.tar.gz"
    FOLDER_NAME="MVE2_OS_355"
    JETSON_LINUX_TARGET_VERSION='35'
    JETSON_LINUX_TARGET_REVISION='5.0'
    BSP_FOLDER_NAME="Linux_for_Tegra_35_5"
elif [ $JETPACK == "36.4" ]; then
    GIT_REPO="git.digotech.net/tvf_agx_orin/operatingsystem/os-364.git"
    BRANCH="v2.0.1"
    ZIPPED_IMAGE="MVE2_OS_364_V2_0_1.tar.gz"
    FOLDER_NAME="MVE2_OS_364"
    JETSON_LINUX_TARGET_VERSION='36'
    JETSON_LINUX_TARGET_REVISION='4.0'
    BSP_FOLDER_NAME="Linux_for_Tegra_36_4"
else
    echo "Invalid jetpack version"
    usage
fi

echo "GIT_REPO: $GIT_REPO"
echo "BRANCH: $BRANCH"

# Check if the branch directory not exist
if [ ! -d "$BRANCH" ]; then
    # Clone the specific branch using embedded credentials
    git clone --branch $BRANCH --single-branch https://${GIT_USERNAME}:${GIT_PASSWORD}@${GIT_REPO} $BRANCH
    # Extract the OS: 
    echo "Extracting the OS from $BRANCH/$ZIPPED_IMAGE to $BSP_FOLDER_NAME"
    sudo rm -rf $BSP_FOLDER_NAME
    mkdir -p $BSP_FOLDER_NAME
    sudo tar xzf  $BRANCH/$ZIPPED_IMAGE -C $BSP_FOLDER_NAME
    # Change folder name to MVE2_OS
    mv $BSP_FOLDER_NAME/$FOLDER_NAME $BSP_FOLDER_NAME/MVE2_OS 
fi

# Download OTA tool
echo "Download OTA tool ..."
wget https://developer.nvidia.com/downloads/embedded/l4t/r${JETSON_LINUX_TARGET_VERSION}_release_v${JETSON_LINUX_TARGET_REVISION}/release/ota_tools_R${JETSON_LINUX_TARGET_VERSION}.${JETSON_LINUX_TARGET_REVISION}_aarch64.tbz2
# Extract OTA tool
rm -rf ota_tools_extracted
echo "Extract OTA tool to ota_tools_extracted/ ..."
mkdir -p ota_tools_extracted
tar -xjf ota_tools_R${JETSON_LINUX_TARGET_VERSION}.${JETSON_LINUX_TARGET_REVISION}_aarch64.tbz2 -C ota_tools_extracted
# Copy OTA tool to the OS
echo "Copy OTA tool to the $BSP_FOLDER_NAME/MVE2_OS/tools/ ..."
sudo rm -rf $BSP_FOLDER_NAME/MVE2_OS/tools/ota_tools
sudo cp -r ota_tools_extracted/Linux_for_Tegra/tools/ota_tools $BSP_FOLDER_NAME/MVE2_OS/tools/