#!/bin/sh

if [ "$EUID" -ne 0 ]
 then echo "Please run as root"
 exit 1
fi

usage() {
    echo "Usage: $0 --version {VERSION, ex: 1.0.0.0} --target_bsp {ex: 35.5, 36.4}"
    exit 1
}

# Check if the correct number of arguments is provided
if [[ "$1" != "--version" ]]; then
    usage
fi

if [[ "$3" != "--target_bsp" ]]; then
    usage
fi

VERSION=$2
TARGET_BSP=$4
echo "TARGET_BSP: $TARGET_BSP"
echo "VERSION: $VERSION"

# Set variables
if [ $TARGET_BSP == "35.5" ]; then
    BSP_FOLDER_NAME="Linux_for_Tegra_35_5"
elif [ $TARGET_BSP == "36.4" ]; then
    BSP_FOLDER_NAME="Linux_for_Tegra_36_4"
else
    echo "Invalid BSP version"
    usage
fi

FILE="$BSP_FOLDER_NAME/MVE2_OS/nv_tegra/user_version"
cat << EOF > "$FILE"
USER_VERSION=$VERSION
EOF

FILE="$BSP_FOLDER_NAME/MVE2_OS/rootfs/etc/user_release_version"
cat << EOF > "$FILE"
# User release: $VERSION
EOF

echo "Version updated to $VERSION"