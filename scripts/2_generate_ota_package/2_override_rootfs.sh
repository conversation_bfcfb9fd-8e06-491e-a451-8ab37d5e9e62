#!/bin/bash

# Function to print usage
usage() {
    echo "Usage: $0 --target_bsp {ex: 35.5, 36.4} --golden_image {ex: mve2.img.raw}"
    exit 1
}

# Check if the correct number of arguments is provided
if [[ "$1" != "--target_bsp" ]]; then
    usage
fi

TARGET_BSP=$2
echo "TARGET_BSP: $TARGET_BSP"

if [[ "$3" != "--golden_image" ]]; then
    usage
fi

GOLDEN_IMAGE=$4
echo "GOLDEN_IMAGE: $GOLDEN_IMAGE"

# Set variables
if [ $TARGET_BSP == "35.5" ]; then
    BSP_FOLDER_NAME=${PWD}/Linux_for_Tegra_35_5/MVE2_OS
elif [ $TARGET_BSP == "36.4" ]; then
    BSP_FOLDER_NAME=${PWD}/Linux_for_Tegra_36_4/MVE2_OS
else
    echo "Invalid BSP version"
    usage
fi

cd $BSP_FOLDER_NAME

# Mount and Extract the Root Filesystem from `mve2.img.raw`
sudo mkdir /mnt/img_mount
sudo mount -o loop $GOLDEN_IMAGE /mnt/img_mount

# Copy the rootfs to the TARGET_BSP
cd $BSP_FOLDER_NAME/rootfs/
sudo rm -rf *
sudo cp -a /mnt/img_mount/* .

# Unmount the image
sudo umount /mnt/img_mount
sudo rm -rf /mnt/img_mount

