#!/bin/bash

# Function to print usage
usage() {
    echo "Usage: $0 --jetpack {ex: 35.5, 36.4}"
    exit 1
}

# Check if the correct number of arguments is provided
if [[ "$1" != "--jetpack" ]]; then
    usage
fi

JETPACK=$2
echo "JETPACK: $JETPACK"

# Set variables
if [ $JETPACK == "35.5" ]; then
    BSP_FOLDER_NAME="Linux_for_Tegra_35_5"
elif [ $JETPACK == "36.4" ]; then
    BSP_FOLDER_NAME="Linux_for_Tegra_36_4"
else
    echo "Invalid jetpack version"
    usage
fi

cd $BSP_FOLDER_NAME/MVE2_OS

# generate the required files
sudo ./flash.sh --no-flash --no-systemimg jetson-agx-orin-devkit mmcblk0p1

# backup image
sudo ROOTFS_AB=1 ./flash.sh -r -k APP -G mve2.img jetson-agx-orin-devkit mmcblk0p1