## Flash Device
Description: flash the device by the target BSP jetpack version, using the base image from Digo
- On the device
	- Reboot in recovery mode
		- Hardware reboot
		- Software reboot: `sudo reboot --force forced-recovery`
- On Host:
	- Go to folder 0_flash_digo_org_image
		- `cd 0_flash_digo_org_image/`
	- Clone target Digo image form gitlab and extract into folder:
		- `bash 0_clone_digo_image.sh --jetpack <version, ex: 35.5, 36.4>`
	- Flash device by target jetpack version
		- `bash 1_flash_org.sh --jetpack <version, ex: 35.5, 36.4>`

## Setup on the device
- On Host:
	- Go to folder `1_init_new_device`
		- `cd 1_init_new_device/`
	- Build worker-ota and worker-record then copy to the device, copy folder ota-data to the device
		- `bash 0_init_new_device.sh --device {ex: mve2@************}`

## Generate golden image (backup rootfs)
- Note:
	- BASE_BSP: base BSP version on the device
	- TARGET_BSP: target BSP version want to upgrade to
	- Example: if you want to upgrade from 35.5 to 36.4, then BASE_BSP=35.5 and TARGET_BSP=36.4
- Pre-required:
	- Flash device in TARGET_BSP version (ex: 35.5.0, 36.4.0, ...) and install all requirement settings
	- Reboot device in recovery mode
- Go to folder `2_generate_ota_package`
	- `cd 2_generate_ota_package/`
- Download and unzip BASE_BSP package from Digo
	- `bash 0_clone_digo_image.sh --jetpack <version, ex: 35.5, 36.4>`
- (Optional) Download and unzip TARGET_BSP package from Digo if BASE_BSP # TARGET_BSP
	- `bash 0_clone_digo_image.sh --jetpack <version, ex: 35.5, 36.4>`
- Backup golden image rootfs from the device, include all the above setting then save into the file `mve2.img.raw` in folder `Linux_for_Tegra_{BASE_BSP}`
	- `bash 1_backup_image.sh --jetpack {ex: 35.5, 36.4}`
- Override the Root Filesystem from backup golden image BASE_BSP above into TARGET_BSP rootfs
	- `bash 2_override_rootfs.sh --target_bsp {ex: 35.5, 36.4} --golden_image {ex: mve2.img.raw}`
- Update OTA version in `Linux_for_Tegra_{TARGET_BSP}/nv_tegra/user_version` and `Linux_for_Tegra_{TARGET_BSP}/rootfs/etc/user_release_version`
	- `bash 3_update_user_version.sh --version {VERSION, ex: *******} --target_bsp {ex: 35.5, 36.4}`
- Generate OTA package:
    - `bash 4_generate_ota_package.sh --base_bsp {ex: 35.5, 36.4} --target_bsp {ex: 35.5, 36.4}`