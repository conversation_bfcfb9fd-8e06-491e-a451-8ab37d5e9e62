#!/bin/bash

# Function to print usage
usage() {
    echo "Usage: $0 --device {ex: mve2@************}"
    exit 1
}

# Check if the correct number of arguments is provided
if [[ "$1" != "--device" ]]; then
    usage
fi

DEVICE_HOST=$2

# Build worker-recording service
echo "Building worker-recording service..."
cd ../../recording_worker/ && make build-deploy
# Copy the worker-recording service to ${device}:./ 
echo "Copying worker-recording service to ${DEVICE_HOST}:./"
scp worker-recording ${DEVICE_HOST}:./

# Build worker-ota service
echo "Building worker-ota service..."
cd ../ && make build-deploy
# Copy the worker-ota service to ${device}:./ 
echo "Copying worker-ota service to ${DEVICE_HOST}:./"
scp worker-ota ${DEVICE_HOST}:./

# Copy the ota-data to ${device}:./
echo "Copying ota-data to ${DEVICE_HOST}:./"
scp -r ota-data ${DEVICE_HOST}:./