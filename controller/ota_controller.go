package controller

import (
	"context"
	"worker-ota/service"

	"go.uber.org/zap"
)

type OtaController struct {
	otaService service.IOtaService
	logger     *zap.Logger
}

func NewOtaController(otaService service.IOtaService, logger *zap.Logger) *OtaController {
	return &OtaController{
		otaService: otaService,
		logger:     logger,
	}
}

func (c *OtaController) SyncFirmware(ctx context.Context) error {
	c.logger.Info("Start sync firmware")
	err := c.otaService.SyncFirmware(ctx)
	c.logger.Info("End sync firmware")
	if err != nil {
		return err
	}

	return nil
}
