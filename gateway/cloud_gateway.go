package gateway

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"
	"worker-ota/config"
	"worker-ota/dto"
	"worker-ota/model"
)

const (
	prefixRoute             = "api/v1/public/"
	syncFirmwareRoute       = "devices/sync"
	syncFirmwareStatusRoute = "devices/sync_status"
	getBaetylInitCmdRoute   = "devices/init_cmd"
)

type ICloudGateway interface {
	SyncFirmwareVersion(ctx context.Context, input SyncFirmwareVersionInput) (model.SyncFirmwareVersionInfo, error)
	SyncFirmwareStatus(ctx context.Context, input SyncFirmwareStatusInput) (model.OtaStatus, error)
	GetBaetylInitCmd(ctx context.Context, macAddress string) (string, error)
}

type CloudGateway struct {
	serverUrl string
}

func NewCloudGateway(config config.OtaConfig) ICloudGateway {
	return &CloudGateway{
		serverUrl: config.CloudUrl,
	}
}

type SyncFirmwareVersionInput struct {
	InternalIp      string
	MacAddress      string
	JetpackVersion  string
	FirmwareVersion string
	DeviceId        string
	DeviceType      model.DeviceType
}

func (g *CloudGateway) SyncFirmwareVersion(ctx context.Context, input SyncFirmwareVersionInput) (model.SyncFirmwareVersionInfo, error) {
	apiUrl := g.serverUrl + prefixRoute + syncFirmwareRoute

	syncRequest := dto.SyncOtaVersionRequest{
		JetpackVersion:    input.JetpackVersion,
		FirmwareVersion:   input.FirmwareVersion,
		DeviceId:          input.DeviceId,
		DeviceType:        input.DeviceType.String(),
		InternalIpAddress: input.InternalIp,
		MacAddress:        input.MacAddress,
	}

	// Marshal the data into json format
	requestBody, err := json.Marshal(syncRequest)
	if err != nil {
		return model.SyncFirmwareVersionInfo{}, err
	}

	request, err := http.NewRequest(http.MethodPut, apiUrl, bytes.NewBuffer(requestBody))
	if err != nil {
		return model.SyncFirmwareVersionInfo{}, err
	}

	request.Header.Add("Content-Type", "application/json")

	// send request
	client := &http.Client{
		Timeout: 60 * time.Second,
	}
	response, err := client.Do(request)
	if err != nil {
		return model.SyncFirmwareVersionInfo{}, err
	}
	defer response.Body.Close()

	if response.StatusCode != http.StatusOK {
		return model.SyncFirmwareVersionInfo{}, fmt.Errorf("sync ota version failed with status code: %d", response.StatusCode)
	}

	bodyResponse, err := io.ReadAll(response.Body)
	if err != nil {
		return model.SyncFirmwareVersionInfo{}, err
	}

	var responseData dto.SyncOtaVersionResponse
	err = json.Unmarshal(bodyResponse, &responseData)
	if err != nil {
		return model.SyncFirmwareVersionInfo{}, err
	}

	syncOutput := model.SyncFirmwareVersionInfo{}

	if responseData.Payload != nil {
		if responseData.Payload.DownloadUrl != nil {
			syncOutput.DownloadUrl = *responseData.Payload.DownloadUrl
		}

		if responseData.Payload.Hash != nil {
			syncOutput.Hash = *responseData.Payload.Hash
		}

		if responseData.Payload.TargetOtaToolUrl != nil {
			syncOutput.TargetOtaToolUrl = *responseData.Payload.TargetOtaToolUrl
		}

		return syncOutput, nil
	}

	return syncOutput, nil
}

type SyncFirmwareStatusInput struct {
	DeviceId   string
	MacAddress string
	Status     model.OtaStatus
}

func (g *CloudGateway) SyncFirmwareStatus(ctx context.Context, input SyncFirmwareStatusInput) (model.OtaStatus, error) {
	apiUrl := g.serverUrl + prefixRoute + syncFirmwareStatusRoute

	syncRequest := dto.SyncOtaStatusRequest{
		DeviceId:   input.DeviceId,
		MacAddress: input.MacAddress,
		Status:     input.Status.String(),
	}

	// Marshal the data into json format
	requestBody, err := json.Marshal(syncRequest)
	if err != nil {
		return "", err
	}

	request, err := http.NewRequest(http.MethodPut, apiUrl, bytes.NewBuffer(requestBody))
	if err != nil {
		return "", err
	}

	request.Header.Add("Content-Type", "application/json")

	// send request
	client := &http.Client{
		Timeout: 60 * time.Second,
	}
	response, err := client.Do(request)
	if err != nil {
		return "", err
	}
	defer response.Body.Close()

	if response.StatusCode != http.StatusOK {
		bodyResponse, err := io.ReadAll(response.Body)
		if err != nil {
			return "", err
		}

		var responseData dto.SyncOtaStatusResponse
		err = json.Unmarshal(bodyResponse, &responseData)
		if err != nil {
			return "", err
		}

		fmt.Println(responseData.Payload.ErrorDescription)

		return "", fmt.Errorf("sync ota status failed with status code: %d", response.StatusCode)
	}

	bodyResponse, err := io.ReadAll(response.Body)
	if err != nil {
		return "", err
	}

	var responseData dto.SyncOtaStatusResponse
	err = json.Unmarshal(bodyResponse, &responseData)
	if err != nil {
		return "", err
	}

	if responseData.Payload != nil {
		if responseData.Payload.Status != nil {
			status := model.OtaStatus(*responseData.Payload.Status)
			if !status.IsValid() {
				return "", fmt.Errorf("sync ota status failed: invalid status")
			}

			return status, nil
		}
	}

	return "", fmt.Errorf("sync ota status failed: empty payload")
}

func (g *CloudGateway) GetBaetylInitCmd(ctx context.Context, macAddress string) (string, error) {
	apiUrl := g.serverUrl + prefixRoute + getBaetylInitCmdRoute

	// Construct the URL with query parameters
	fullURL, err := url.Parse(apiUrl)
	if err != nil {
		return "", fmt.Errorf("error parsing base url: %v", err)
	}

	// Add query parameters to the URL
	query := fullURL.Query()
	query.Set("mac_address", macAddress)
	fullURL.RawQuery = query.Encode()
	fmt.Println(fullURL.String())
	request, err := http.NewRequest(http.MethodGet, fullURL.String(), nil)
	if err != nil {
		return "", err
	}

	// send request
	client := &http.Client{
		Timeout: 60 * time.Second,
	}
	response, err := client.Do(request)
	if err != nil {
		return "", err
	}
	defer response.Body.Close()

	if response.StatusCode != http.StatusOK {
		return "", fmt.Errorf("get baetyl init cmd failed with status code: %d", response.StatusCode)
	}

	bodyResponse, err := io.ReadAll(response.Body)
	if err != nil {
		return "", err
	}

	var responseData dto.GetBaetylInitCmdResponse
	err = json.Unmarshal(bodyResponse, &responseData)
	if err != nil {
		return "", err
	}

	if responseData.Payload != nil {
		if responseData.Payload.InitCmd != nil {
			return *responseData.Payload.InitCmd, nil
		}
	}

	return "", fmt.Errorf("get baetyl init cmd failed")
}
