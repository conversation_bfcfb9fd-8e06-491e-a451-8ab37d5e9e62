# Generate OTA Golden Image Base
## 1. Flash new Jetson device with sample rootfs: 
[flash_new_device](FlashNewDevice.md)

## 2. Install requirement, Apply the customizations needed in Jetson device
- Go to Jetson device
- Copy `ota-data` folder to folder `/etc/ota-data`
- Run base [install_requirement.sh](ota-data/install_requirement.sh) to install all pre-require libs, mount data folder, setup Docker and Nvidia container
- Disconnect to internet (to avoid worker-ota call to cloud API and auto run init cmd)
- Build worker-ota service and copy binary file `worker-ota` to `/home/<USER>
- Create and start worker-ota service by [setup_worker_ota_service.sh](ota-data/setup_worker_ota_service.sh)

## 3. Put Jetson device in recovery mode 
+ Method 1: Jetson already has OS installed. Open Terminal and type the command: `sudo reboot --force forced-recovery`
+ Method 2: Jetson does not have OS installed. Press and hold the RECOVERY button (1) and press the SYS_RST button (2) for 1 second, then release the SYS_RST button (2). After releasing the SYS_RST button (2), continue to release the RECOVERY button (1) and proceed to the next step.
## 4. Clone of the rootfs partition
Go to [scripts](scripts)

Run prepare script:

    bash prepare_build_fw.sh

Run clone rootfs script:

    cd <path-to-Linux_for_Tegra-directory>

    # generate the required files
    sudo ./flash.sh --no-flash --no-systemimg jetson-agx-orin-devkit mmcblk0p1

    # backup image
    sudo ROOTFS_AB=1 ./flash.sh -r -k APP -G mve2.img jetson-agx-orin-devkit mmcblk0p1

## 5. Generate OTA package with Golden Rootfs Image
Go to `Linux_for_Tegra/nv_tegra/user_version`, update `USER_VERSION` as firmware version (ex: *******)

Run command line:

    sudo ./tools/ota_tools/version_upgrade/l4t_generate_ota_package.sh -s -f mve2.img -r jetson-agx-orin-devkit R35-5

or

    sudo ./tools/ota_tools/version_upgrade/l4t_generate_ota_package.sh -s -f mve2.img.raw -r jetson-agx-orin-devkit R35-5


Ref: [A/B Filesystem Redundancy and OTA Updates with NVIDIA Jetpack | RidgeRun Developer ](https://developer.ridgerun.com/wiki/index.php/How_to_Use_A/B_Filesystem_Redundancy_and_OTA_with_NVIDIA_Jetpack#Root_Filesystem_Customization_and_Update)