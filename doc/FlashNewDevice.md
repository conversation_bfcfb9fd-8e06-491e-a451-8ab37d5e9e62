# 1. Prepare
Run [script](scripts/prepare_flash.sh) to prepare env
    
    bash prepare_flash.sh
    
Note: Parameters need to change for next release version
- TARGET_BOARD: the type of target device will deploy this firmware

        For Jetson Xavier NX P3668-0001: jetson-xavier-nx-devkit-emmc
        For Jetson AGX Xavier P2888-0001 (16GB) or P2888-0004 (32GB) or P2888-0005 (64GB): jetson-agx-xavier-devkit
        For Jetson AGX Xavier Industrial P2888-0008 (64GB): jetson-agx-xavier-industrial
        For Jetson AGX Orin P3701-0000(32GB) or P3701-0004 (32GB) or P3701-0005 (64GB): jetson-agx-orin-devkit
        For Jetson AGX Orin Industrial P3701-0008: jetson-agx-orin-devkit-industrial
        For Jetson Orin NX P3767-0000(16GB) or P3767-0001(8GB): jetson-orin-nano-devkit
        For Jetson Orin Nano P3767-0003(8GB) or P3767-0004(4GB) or P3767-0005(8GB): jetson-orin-nano-devkit
- JETSON_LINUX_RELEASE_VERSION : the Jetson Linux release version of upgrade package
- JETSON_LINUX_REVISION: the Jetson Linux release revision of upgrade package
- BSP_VERSION: base BSP which the target devices are running on

# 2. Put your device in recovery mode 
- Unplug power, press and hold middle button while plugging in to power. Once the light is up, let go of the button.

- Verify by running `lsusb` and you should see Nvidia APX

# 3. Flashing
Go to Linux_for_Tegra and run this command from your host machine : 
    
    cd Linux_for_Tegra
    
    ROOTFS_AB=1 sudo ./flash.sh jetson-agx-orin-devkit mmcblk0p1