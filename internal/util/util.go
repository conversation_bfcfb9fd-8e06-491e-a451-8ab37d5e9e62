package util

import (
	"bytes"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"io"
	"net"
	"net/url"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"
	"worker-ota/model"
)

func FileExists(filename string) bool {
	_, err := os.Stat(filename)
	if os.IsNotExist(err) {
		return false
	}
	return err == nil
}

// GetHashFile computes the SHA-256 hash of the file at the specified path and returns it as a hexadecimal string.
func GetHashFile(filepath string) (string, error) {
	file, err := os.Open(filepath)
	if err != nil {
		return "", fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	hash := sha256.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", fmt.Errorf("failed to compute hash: %w", err)
	}

	hashedBytes := hash.Sum(nil)
	return hex.EncodeToString(hashedBytes), nil
}

func CompareHashKey(filepath string, hashkey string) (bool, error) {
	fileHash, err := GetHashFile(filepath)
	if err != nil {
		return false, err
	}

	if fileHash == hashkey {
		return true, nil
	}
	return false, nil
}

func GetMACAddresses(iName string) (string, error) {
	interfaces, err := net.Interfaces()
	macStr := ""

	if err != nil {
		fmt.Printf("Error fetching network interfaces: %v\n", err)
		return "", fmt.Errorf("get network interface failed")
	}

	for _, iface := range interfaces {
		mac := iface.HardwareAddr
		if mac != nil {
			if iface.Name == iName {
				macStr = mac.String()
				fmt.Printf("Interface: %s, MAC address: %s\n", iface.Name, mac.String())
			}
		}
	}

	return macStr, nil
}

func GetGSMIFFromNMCLI(device string) (string, error) {
	fmt.Printf("Getting IP address for wwan0 using nmcli\n")
	// Run nmcli command to get IP address
	command := fmt.Sprintf("nmcli -t -f IP4.ADDRESS device show %s | cut -d':' -f2 | cut -d'/' -f1", device)
	cmd := exec.Command("sh", "-c", command)

	// Capture output
	var out bytes.Buffer
	cmd.Stdout = &out

	// Run the command
	err := cmd.Run()
	if err != nil {
		return "", fmt.Errorf("failed to get IP from wwan0 using nmcli: %v", err)
	}

	// Trim spaces and return IP
	ip := strings.TrimSpace(out.String())
	if ip == "" {
		return "", fmt.Errorf("no IP address found for wwan0")
	}

	return ip, nil
}

func GetIPAddresses() ([]string, error) {
	interfaces, err := net.Interfaces()
	ipsAddress := []string{}

	if err != nil {
		fmt.Printf("Error fetching network interfaces: %v\n", err)
		return nil, fmt.Errorf("get network interface failed")
	}

	for _, iface := range interfaces {
		fmt.Println("Found interface:", iface.Name)
	}

	networkInterface := []string{
		"eth0",
		"eth1",
		"eno1",
		"wlan0",
		"wwan0",
		"ppp0",
		"cdc-wdm0",
	}

	for _, desiredInterface := range networkInterface {
		for _, iface := range interfaces {
			if iface.Name != desiredInterface {
				continue
			}
			// Get MAC address
			mac := iface.HardwareAddr
			if mac != nil {
				// Get IP addresses
				addrs, err := iface.Addrs()
				if err != nil {
					fmt.Printf("Error fetching addresses for interface %s: %v\n", iface.Name, err)
					continue
				}

				for _, addr := range addrs {
					ipnet, ok := addr.(*net.IPNet)
					if !ok {
						continue
					}
					ip := ipnet.IP
					if ip.To4() != nil { // Filter for IPv4 addresses
						ipStr := ip.String()
						ipsAddress = append(ipsAddress, ipStr)
					}
				}
			}
		}
	}

	ip, err := GetGSMIFFromNMCLI("cdc-wdm0")
	if err != nil {
		fmt.Printf("Error fetching IP address for wwan0: %v\n", err)
	} else {
		fmt.Printf("IP address for wwan0: %s\n", ip)
		ipsAddress = append(ipsAddress, ip)
	}

	return ipsAddress, nil
}

func GetIpMacAddress() ([]model.NetworkInfo, error) {
	macStr, err := GetMACAddresses("eth0")
	if err != nil {
		return nil, err
	}
	ipsAddress, err := GetIPAddresses()
	if err != nil {
		return nil, err
	}

	fmt.Printf("MAC address: %s\n", macStr)
	fmt.Printf("IP address: %v\n", ipsAddress)

	if len(ipsAddress) == 0 {
		return nil, fmt.Errorf("no ip address found")
	}

	ipsToMacs := []model.NetworkInfo{}
	ipMacAddress := model.NetworkInfo{
		Ip:  ipsAddress[0],
		Mac: macStr,
	}
	ipsToMacs = append(ipsToMacs, ipMacAddress)

	return ipsToMacs, nil

}

func GetJetsonLinuxReleaseVersion() (string, error) {
	// Define the file path
	filePath := "/etc/nv_tegra_release"

	// Read the file content
	content, err := os.ReadFile(filePath)
	if err != nil {
		return "", fmt.Errorf("error reading file: %v", err)
	}

	// Compile the regular expressions
	releaseRegex := regexp.MustCompile(`R\d+`)

	// Find the matches
	releaseMatch := releaseRegex.FindString(string(content))

	jp_version := strings.ToLower(releaseMatch)
	jp_version = strings.Replace(jp_version, "r", "", 1)

	return jp_version, nil
}

func GetJetsonLinuxReVision() (string, error) {
	// Define the file path
	filePath := "/etc/nv_tegra_release"

	// Read the file content
	content, err := os.ReadFile(filePath)
	if err != nil {
		return "", fmt.Errorf("error reading file: %v", err)
	}

	// Compile the regular expressions
	revisionRegex := regexp.MustCompile(`REVISION: \d+\.\d+`)

	// Find the matches
	revisionMatch := revisionRegex.FindString(string(content))

	// Extract the revision number
	if len(revisionMatch) > 10 {
		revisionMatch = revisionMatch[10:]
	}

	jp_version := strings.ToLower(revisionMatch)

	return jp_version, nil
}

func GetJetpackVersion() (string, error) {
	releaseVer, _ := GetJetsonLinuxReleaseVersion()
	reVision, _ := GetJetsonLinuxReVision()

	jp_version := strings.ToLower(releaseVer + "." + reVision)

	return jp_version, nil
}

func GetFirmwareVersion() (string, error) {
	// Define the file path
	filePath := "/etc/user_release_version"

	// Read the file content
	content, err := os.ReadFile(filePath)
	if err != nil {
		return "", fmt.Errorf("error reading file: %v", err)
	}

	fw_version := strings.Split(string(content), ":")[1]
	fw_version = strings.ReplaceAll(fw_version, "\n", "")
	fw_version = strings.ReplaceAll(fw_version, " ", "")

	return fw_version, nil
}

func GetDeviceId() (string, error) {
	// Define the file path
	filePath := "/home/<USER>/.device_id"

	if FileExists(filePath) {
		// Read the file content
		content, err := os.ReadFile(filePath)
		if err != nil {
			return "", fmt.Errorf("error reading file: %v", err)
		}
		strContent := strings.ReplaceAll(string(content), "\n", "")
		strContent = strings.ReplaceAll(strContent, " ", "")
		return strContent, nil
	}

	return "", fmt.Errorf("device Id file not found")
}

func ExtractFileNameFromUrl(urlStr string) (string, error) {
	// Parse the URL
	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return "", fmt.Errorf("error parsing url: %v", err)
	}

	// Extract the path from the URL
	path := parsedURL.Path

	// Extract the file name from the path
	fileName := filepath.Base(path)

	return fileName, nil
}

func CreateFolders(path string) error {
	if _, err := os.Stat(path); os.IsNotExist(err) {
		// Directory does not exist, create it
		err := os.MkdirAll(path, os.ModePerm)
		if err != nil {
			return fmt.Errorf("error creating directory: %v", err)
		}
	}

	return nil
}
