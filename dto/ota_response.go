package dto

type SyncOtaVersionResponse struct {
	Status  int                `json:"status" binding:"required"`
	Message string             `json:"message" binding:"required"`
	Payload *SyncVersionStruct `json:"payload"`
}

type SyncVersionStruct struct {
	ErrorDescription interface{} `json:"errors"`
	DownloadUrl      *string     `json:"download_url"`
	Hash             *string     `json:"hash"`
	TargetOtaToolUrl *string     `json:"target_ota_tool_url"`
}

type SyncOtaStatusResponse struct {
	Status  int               `json:"status" binding:"required"`
	Message string            `json:"message" binding:"required"`
	Payload *SyncStatusStruct `json:"payload"`
}

type SyncStatusStruct struct {
	ErrorDescription interface{} `json:"errors"`
	Status           *string     `json:"status"`
}

type GetBaetylInitCmdResponse struct {
	Status  int                  `json:"status" binding:"required"`
	Message string               `json:"message" binding:"required"`
	Payload *BaetylInitCmdStruct `json:"payload"`
}

type BaetylInitCmdStruct struct {
	ErrorDescription interface{} `json:"errors"`
	InitCmd          *string     `json:"init_cmd"`
}
