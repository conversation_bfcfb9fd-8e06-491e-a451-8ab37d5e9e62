package dto

type SyncOtaVersionRequest struct {
	JetpackVersion    string `json:"jetpack_version"`
	FirmwareVersion   string `json:"firmware_version"`
	DeviceId          string `json:"device_id"`
	DeviceType        string `json:"device_type"`
	InternalIpAddress string `json:"internal_ip_address"`
	MacAddress        string `json:"mac_address"`
}

type SyncOtaStatusRequest struct {
	DeviceId   string `json:"device_id" binding:"required" example:"00:1b:44:11:3a:b7"`
	MacAddress string `json:"mac_address" binding:"required" example:"************"`
	Status     string `json:"status" binding:"required"`
}
