package config

// Configurations contains the configuration for different services, it will be loaded automatically by viper
type Configurations struct {
	Ota   OtaConfig `mapstructure:"ota"`
	Debug bool      `mapstructure:"debug"`
}

type OtaConfig struct {
	CloudUrl         string `mapstructure:"cloudUrl"`
	DeviceType       string `mapstructure:"deviceType"`
	TimeSyncFirmware int    `mapstructure:"timeSyncFirmware"`
	PathKubeConfig   string `mapstructure:"pathKubeConfig"`
	PathInitCmd      string `mapstructure:"pathInitCmd"`
}
