package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"
	"worker-ota/config"
	"worker-ota/controller"
	"worker-ota/gateway"
	"worker-ota/internal/logger"
	"worker-ota/model"
	"worker-ota/service"

	"github.com/spf13/viper"
	"go.uber.org/zap"
)

const (
	configFile  = "/etc/ota-data/worker_ota_config.yaml"
	minInterval = 3
)

// initLogger creates a new zap. Logger
func initLogger(configs config.Configurations) *zap.Logger {
	return logger.NewLogger(configs)
}

// initConfig initializes the config.
func initConfig() *config.Configurations {
	viper.SetConfigType("yaml")

	// Expand environment variables inside the config file
	fmt.Println("configFile: " + configFile)
	b, err := os.ReadFile(configFile)
	if err != nil {
		fmt.Printf("read config has failed failed with error: %v\n", err)
		os.Exit(1)
	}

	expand := os.ExpandEnv(string(b))
	configReader := strings.NewReader(expand)

	viper.AutomaticEnv()

	if err := viper.ReadConfig(configReader); err != nil {
		fmt.Printf("read config has failed with error: %v\n", err)
		os.Exit(1)
	}

	configs := config.Configurations{}
	if err := viper.Unmarshal(&configs); err != nil {
		fmt.Printf("read config has failed failed with error: %v\n", err)
		os.Exit(1)
	}

	return &configs
}

func main() {
	configs := initConfig()
	fmt.Println(configs.Ota.CloudUrl)
	logger := initLogger(*configs)

	deviceType := model.DeviceType(configs.Ota.DeviceType)
	if !deviceType.IsValid() {
		fmt.Println("device type not valid")
		os.Exit(1)
	}

	// Init interface
	cloudGw := gateway.NewCloudGateway(configs.Ota)
	otaService := service.NewOtaService(cloudGw, deviceType, logger, configs.Ota)
	otaCtrl := controller.NewOtaController(otaService, logger)

	ctx := context.Background()
	go func() {
		for {
			select {
			case <-ctx.Done():
				return
			default:
				start := time.Now()
				otaCtrl.SyncFirmware(ctx)
				elapsed := time.Since(start)

				customInterval := configs.Ota.TimeSyncFirmware - int(elapsed.Seconds())
				if customInterval < minInterval {
					customInterval = minInterval
				}

				select {
				case <-time.After(time.Duration(customInterval) * time.Second):
					continue
				case <-ctx.Done():
					return
				}
			}
		}
	}()

	// Graceful shutdown
	idleConnectionsClosed := make(chan struct{})
	go func() {
		c := make(chan os.Signal, 1)
		signal.Notify(c, os.Interrupt, syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT)

		<-c

		_, cancel := context.WithTimeout(ctx, 15*time.Second)
		defer cancel()

		close(idleConnectionsClosed)
	}()
	<-idleConnectionsClosed
}
