package model

import (
	"fmt"
	"time"

	"go.uber.org/zap"
)

type OtaStatus string

const (
	OtaStatusPending   OtaStatus = "PENDING"
	OtaStatusUpgrading OtaStatus = "UPGRADING"
	OtaStatusSucceeded OtaStatus = "SUCCEEDED"
	OtaStatusFailed    OtaStatus = "FAILED"
	OtaStatusCanceled  OtaStatus = "CANCELED"
)

var ListOtaStatus = []OtaStatus{
	OtaStatusPending,
	OtaStatusUpgrading,
	OtaStatusSucceeded,
	OtaStatusFailed,
	OtaStatusCanceled,
}

func (OtaStatus) Values() (kinds []string) {
	for _, s := range ListOtaStatus {
		kinds = append(kinds, string(s))
	}
	return
}

func (o OtaStatus) IsValid() bool {
	switch o {
	case OtaStatusPending,
		OtaStatusUpgrading,
		OtaStatusSucceeded,
		OtaStatusFailed,
		OtaStatusCanceled:
		return true
	default:
		return false
	}
}

func (o OtaStatus) String() string {
	return string(o)
}

type SyncFirmwareVersionInfo struct {
	DownloadUrl      string
	Hash             string
	TargetOtaToolUrl string
}

type ProgressWriter struct {
	Total      int64
	Completed  int64
	AnchorTime time.Time
	Logger     *zap.Logger
}

func (pw *ProgressWriter) Write(p []byte) (int, error) {
	n := len(p)
	pw.Completed += int64(n)
	logInterval := 10 * time.Second
	percentage := float64(pw.Completed) / float64(pw.Total) * 100
	if time.Since(pw.AnchorTime) > logInterval || percentage >= 100 {
		pw.AnchorTime = time.Now()
		percentageStr := fmt.Sprintf("%.2f", percentage)
		pw.Logger.Info("Progress: " + percentageStr + "%")
	}
	return n, nil
}
