package model

type DeviceType string

const (
	JETSON_AGX_ORIN DeviceType = "Jetson AGX Orin"
)

var ListDeviceType = []DeviceType{JETSON_AGX_ORIN}

func (DeviceType) Values() (kinds []string) {
	for _, s := range ListDeviceType {
		kinds = append(kinds, string(s))
	}
	return
}

func (d DeviceType) String() string {
	return string(d)
}

func (d DeviceType) IsValid() bool {
	switch d {
	case JETSON_AGX_ORIN:
		return true
	default:
		return false
	}
}
