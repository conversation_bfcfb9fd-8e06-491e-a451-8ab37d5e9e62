#!/bin/bash

if [ "$EUID" -ne 0 ]
 then echo "Please run as root"
 exit 
fi

DEVICE_ID_FILE="/home/<USER>/.device_id"
PASSWORD="mve2@321"

WIFI_IF="muap0"
MOBILE_IF=wwan0
ETHERNET_IF=eth0

ip_forwarding(){
    # Enable IP Forwarding
    sysctl -w net.ipv4.ip_forward=1

    # Set Up NAT (Masquerading)
    iptables -t nat -A POSTROUTING -o ${MOBILE_IF} -j MASQUERADE
    iptables -t nat -A POSTROUTING -o ${ETHERNET_IF} -j MASQUERADE
    # Allow Forwarding from Wi-Fi to Internet
    iptables -A FORWARD -i ${WIFI_IF} -o ${MOBILE_IF} -j ACCEPT
    iptables -A FORWARD -i ${WIFI_IF} -o ${ETHERNET_IF} -j ACCEPT
    # Allow Established Connections Back
    iptables -A FORWARD -i ${MOBILE_IF} -o ${WIFI_IF} -m state --state ESTABLISHED,RELATED -j ACCEPT
    iptables -A FORWARD -i ${ETHERNET_IF} -o ${WIFI_IF} -m state --state ESTABLISHED,RELATED -j ACCEPT
}

setup_ap(){
    ID=$1
    CONN_NAME=orin-ap-${ID}
    
    sleep 1

    # Disable station mode
    if nmcli -t -f DEVICE,STATE device | grep -q '^mlan0:connected$'; then
        echo "mlan0 is active"
        sleep 1
        nmcli device disconnect mlan0
        CONN_NAME=$(nmcli -t -f NAME,DEVICE con show | grep 'mlan0' | cut -d: -f1)
        sleep 1
        # Check if a connection was found
        if [ -n "$CONN_NAME" ]; then
            echo "Deleting connection: $CONN_NAME"
            nmcli con delete "$CONN_NAME"
            echo "Connection deleted successfully."
        else
            echo "No connection found for mlan0."
        fi
    else
        echo "mlan0 is not active"
    fi

    sleep 1
    if nmcli -t -f DEVICE,STATE device | grep -q '^mmlan0:connected$'; then
        echo "mmlan0 is active"
        sleep 1
        nmcli device disconnect mmlan0
        CONN_NAME=$(nmcli -t -f NAME,DEVICE con show | grep 'mmlan0' | cut -d: -f1)
        sleep 1
        # Check if a connection was found
        if [ -n "$CONN_NAME" ]; then
            echo "Deleting connection: $CONN_NAME"
            nmcli con delete "$CONN_NAME"
            echo "Connection deleted successfully."
        else
            echo "No connection found for mmlan0."
        fi
    else
        echo "mmlan0 is not active"
    fi

    # Create AP using muap0 interface
    nmcli device wifi hotspot ifname ${WIFI_IF} con-name ${CONN_NAME} ssid ${CONN_NAME}

    # Set WiFi settings
    nmcli con modify ${CONN_NAME} 802-11-wireless.band bg
    nmcli con modify ${CONN_NAME} 802-11-wireless-security.key-mgmt wpa-psk
    nmcli con modify ${CONN_NAME} 802-11-wireless-security.psk ${PASSWORD}

    # Configure IP & Internet Sharing
    nmcli con modify ${CONN_NAME} ipv4.method shared
    nmcli con modify ${CONN_NAME} ipv4.addresses ***********00/24
    nmcli con modify ${CONN_NAME} ipv4.gateway ***********
    nmcli con modify ${CONN_NAME} ipv4.route-metric 700

    # Enable auto-connect
    nmcli con modify ${CONN_NAME} connection.autoconnect yes
    nmcli con modify ${CONN_NAME} connection.autoconnect-priority 100

    # Activate the AP
    nmcli con up ${CONN_NAME}

    ip_forwarding
}

run (){
    # Check if the file exists
    DEVICE_ID=""
    if [[ ! -f "$DEVICE_ID_FILE" ]]; then
        echo "File $DEVICE_ID_FILE is not existed"
        DEVICE_ID="default"
    else
        read -r DEVICE_ID < "$DEVICE_ID_FILE"
        ID="${DEVICE_ID: -7}"
        echo "ID: $ID"
        DEVICE_ID=$ID
    fi

    # Check if connection to muap0 device is existed
    if nmcli -t -f DEVICE con show | grep -q "^muap0$"; then
        echo "Connection muap0 exists."
        # If existed, check the name of this connection
        CONN_NAME=$(nmcli -t -f NAME,DEVICE con show | grep ":muap0$" | cut -d: -f1)
        echo "Connection Name for muap0: $CONN_NAME"
        # If the name is not matched with the device id, delete the connection and create a new one
        if [[ "$CONN_NAME" != "orin-ap-$DEVICE_ID" ]]; then
            echo "Connection Name is not matched with the device id."
            nmcli con delete $CONN_NAME
            setup_ap $DEVICE_ID
        fi
    else
        # If not, create a new one
        echo "Connection muap0 does NOT exist."
        # Check if the file exists
        if [[ ! -f "$DEVICE_ID_FILE" ]]; then
            echo "File $DEVICE_ID_FILE is not existed"
            setup_ap "default"
        else
            read -r DEVICE_ID < "$DEVICE_ID_FILE"
            ID="${DEVICE_ID: -7}"
            echo "ID: $ID"
            setup_ap $ID
        fi
    fi
}

run