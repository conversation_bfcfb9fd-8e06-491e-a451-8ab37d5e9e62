#!/bin/bash

# global var 
CONN_NAME=eight-mobile
APN_NAME=shwap
IF_NAME=cdc-wdm0

# Setting up 8eight sim card
nmcli c add type gsm con-name ${CONN_NAME} ifname ${IF_NAME} apn ${APN_NAME}

# Setting up the properties 
nmcli con modify ${CONN_NAME} connection.autoconnect yes
nmcli con modify ${CONN_NAME} connection.autoconnect-priority 10
nmcli con modify ${CONN_NAME} connection.autoconnect-retries 3
nmcli con modify ${CONN_NAME} ipv4.route-metric 400

# Up the connection
nmcli con up ${CONN_NAME}

# Ensure that the radio interface is on
nmcli r wwan on
