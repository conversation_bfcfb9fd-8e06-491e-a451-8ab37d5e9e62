#!/bin/sh

update_config(){
    sudo bash -c 'cat > /etc/logrotate.d/rsyslog << EOF
/var/log/syslog
/var/log/daemon.log
/var/log/kern.log
/var/log/cron.log
/var/log/debug
/var/log/messages
{
	rotate 5
	size 100M
	missingok
	notifempty
	compress
	delaycompress
	sharedscripts
	postrotate
		/usr/lib/rsyslog/rsyslog-rotate
	endscript
}
EOF'

    sudo bash -c 'cat > /etc/logrotate.conf << EOF
rotate 3
daily
missingok
notifempty
delaycompress
compress
postrotate
    /usr/lib/rsyslog/rsyslog-rotate
endscript
su root adm
create
include /etc/logrotate.d
EOF'
}

# Cron job muốn ghi đè (chạy logrotate mỗi giờ)
CRON_JOB="0 * * * * /usr/sbin/logrotate /etc/logrotate.conf"
# File crontab của root
CRONTAB_FILE="/var/spool/cron/crontabs/root"

# Hàm kiểm tra quyền root
check_root() {
    if [ "$EUID" -ne 0 ]; then
        echo "Error: This script must be run as root or with sudo. Use 'sudo ./overwrite_crontab.sh'"
        exit 1
    fi
}

# Hàm sao lưu file crontab hiện tại
backup_crontab() {
    local backup_file="${CRONTAB_FILE}.bak_$(date +%Y%m%d_%H%M%S)"
    if [ -f "$CRONTAB_FILE" ]; then
        cp "$CRONTAB_FILE" "$backup_file"
        echo "Backup of $CRONTAB_FILE created as $backup_file"
    else
        echo "Warning: $CRONTAB_FILE does not exist, no backup created."
    fi
}

# Hàm ghi đè file crontab với nội dung mới
overwrite_crontab() {
    echo "Overwriting $CRONTAB_FILE with new cron job..."

    # Ghi đè file với cron job duy nhất
    echo "$CRON_JOB" > "$CRONTAB_FILE"
    if [ $? -eq 0 ]; then
        echo "Successfully overwrote $CRONTAB_FILE with:"
        echo "$CRON_JOB"
    else
        echo "Error: Failed to overwrite $CRONTAB_FILE."
        exit 1
    fi

    # Cập nhật crontab để cron nhận diện thay đổi
    crontab "$CRONTAB_FILE"
    if [ $? -eq 0 ]; then
        echo "Crontab updated successfully."
    else
        echo "Error: Failed to update crontab."
        exit 1
    fi
}

# Hàm kiểm tra kết quả
verify_crontab() {
    echo "Verifying crontab configuration:"
    crontab -l
}


run(){
    update_config
    echo "Starting to overwrite /var/spool/cron/crontabs/root for logrotate to run every hour..."
    check_root
    backup_crontab
    overwrite_crontab
    verify_crontab
    echo "Script completed successfully."
}

run