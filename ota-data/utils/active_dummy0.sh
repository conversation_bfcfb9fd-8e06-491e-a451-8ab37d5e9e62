#!/bin/bash

create_dummy_connection(){
    CONN_NAME="dummy0"

    # Create the Dummy Interface if not exists
    if ! nmcli con show $CONN_NAME > /dev/null 2>&1; then
        nmcli con add type dummy ifname $CONN_NAME con-name $CONN_NAME
        nmcli con modify $CONN_NAME ipv4.method shared
        nmcli con modify $CONN_NAME ipv4.addresses ***************/24
        nmcli con modify $CONN_NAME ipv4.gateway *************
        nmcli con modify $CONN_NAME ipv4.route-metric 700
    fi

    nmcli connection up dummy0
}

setup_dummy_interface(){
  # Define the script to run after reboot
  DUMMY0_SCRIPT="/usr/local/bin/activate_dummy0.sh"

  # Create the activation script
  cat << 'EOF' | sudo tee "$DUMMY0_SCRIPT" > /dev/null
#!/bin/bash

# Maximum attempts to bring dummy0 up
MAX_ATTEMPTS=10
ATTEMPT=1
CONN_NAME="dummy0"

# Create the Dummy Interface if not exists
if ! nmcli con show $CONN_NAME > /dev/null 2>&1; then
    nmcli con add type dummy ifname $CONN_NAME con-name $CONN_NAME
    nmcli con modify $CONN_NAME ipv4.method shared
    nmcli con modify $CONN_NAME ipv4.addresses ***************/24
    nmcli con modify $CONN_NAME ipv4.gateway *************
    nmcli con modify $CONN_NAME ipv4.route-metric 700
fi

# Check if dummy0 is UP, retry until it is or max attempts reached
while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
    # Check the state of dummy0
    if ip link show dummy0 | grep -q "state UP"; then
        echo "dummy0 is UP after $ATTEMPT attempts" >> /var/log/dummy0_cron.log
        exit 0
    fi

    # Attempt to bring dummy0 up
    /usr/bin/nmcli connection up dummy0 >> /var/log/dummy0_cron.log 2>&1

    # Wait a bit before the next attempt
    sleep 5

    # Increment attempt counter
    ATTEMPT=$((ATTEMPT + 1))
done

echo "Failed to bring dummy0 UP after $MAX_ATTEMPTS attempts" >> /var/log/dummy0_cron.log
exit 1
EOF

  # Make the activation script executable
  sudo chmod +x "$DUMMY0_SCRIPT"

  # Add the cronjob to run the script at reboot
  (crontab -l 2>/dev/null; echo "@reboot $DUMMY0_SCRIPT") | crontab -

  echo "Cronjob created to run $DUMMY0_SCRIPT at reboot."
}


echo "Create Dummy Connection..."
create_dummy_connection

echo "Setup Dummy Interface..."
setup_dummy_interface