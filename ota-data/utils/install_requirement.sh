#!/bin/sh
SUDO=sudo

exec_cmd_nobail() {
  echo "+ $2 bash -c \"$1\""
  $2 bash -c "$1"
}
print_status() {
  echo "## $1"
}

# Function to install pre-requisite package
setup_prerequisite() {
  sudo timedatectl set-timezone "Asia/Singapore"
  exec_cmd_nobail "apt-get update" $SUDO
  exec_cmd_nobail "apt-get install -y curl" $SUDO
  exec_cmd_nobail "apt-get install -y ffmpeg" $SUDO
  exec_cmd_nobail "apt install --only-upgrade firefox" $SUDO
  exec_cmd_nobail "apt install logrotate -y" $SUDO
  exec_cmd_nobail "mkdir -p /home/<USER>/.kube"
}

# Function to setup gsm
setup_gsm(){
  exec_cmd_nobail "bash /etc/ota-data/utils/setup-gsm.sh" $SUDO
  sudo chmod +x /usr/bin/qwan_util
}

# Function to setup ap
setup_ap(){
  exec_cmd_nobail "bash /etc/ota-data/utils/setup-ap.sh" $SUDO
}

# Function to setup worker-record service
setup_worker_record_service() {
  exec_cmd_nobail "bash /etc/ota-data/services/setup_worker_record_service.sh" $SUDO
}

# Function to setup worker-ota service
# Stop it right away, let it start in the next times
setup_worker_ota_service() {
  exec_cmd_nobail "bash /etc/ota-data/services/setup_worker_ota_service.sh && systemctl stop worker-ota" $SUDO
}

# Function to setup Nvidia container toolkit
setup_nvidia_container() {
  exec_cmd_nobail "curl -fsSL https://nvidia.github.io/libnvidia-container/gpgkey | sudo gpg --dearmor -o /usr/share/keyrings/nvidia-container-toolkit-keyring.gpg \
  && curl -s -L https://nvidia.github.io/libnvidia-container/stable/deb/nvidia-container-toolkit.list | \
    sed 's#deb https://#deb [signed-by=/usr/share/keyrings/nvidia-container-toolkit-keyring.gpg] https://#g' | \
    sudo tee /etc/apt/sources.list.d/nvidia-container-toolkit.list"
  exec_cmd_nobail "apt-get update" $SUDO
  exec_cmd_nobail "apt-get install -y nvidia-container-toolkit" $SUDO
}

# Config Logrotate
config_logrotate() {
  exec_cmd_nobail "bash /etc/ota-data/utils/setup-logrotate.sh" $SUDO
}

config_xorg(){
  exec_cmd_nobail "bash /etc/ota-data/utils/setup-xorg.sh" $SUDO
}

echo "Start to install requirement..."

echo "Install pre-requisite package"
setup_prerequisite
echo "Setup gsm..."
setup_gsm
echo "Setup ap..."
setup_ap
echo "Setup worker-record..."
setup_worker_record_service
echo "Setup worker-ota..."
setup_worker_ota_service
echo "Setting up Nvidia container toolkit"
setup_nvidia_container
echo "Config logrotate"
config_logrotate
echo "Config xorg"
config_xorg

echo "Done!"