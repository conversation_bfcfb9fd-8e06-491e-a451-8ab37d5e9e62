# #!/bin/bash

# # if /dev/sda exists, use it, otherwise use /dev/nvme0n1
# if [ -b "/dev/sda" ]; then
#     DISK="/dev/sda"
#     PARTITION="${DISK}1"
#     MOUNT_POINT="/data"
# else
#     DISK="/dev/nvme0n1"
#     PARTITION="${DISK}p1"
#     MOUNT_POINT="/data"
# fi

# # Update /etc/fstab for automatic mounting
# update_fstab() {
#   echo "Updating /etc/fstab..."
#   if ! mount | grep -q "$PARTITION on /data"; then
#     echo "Mounting $PARTITION to /data..."
#     echo "$PARTITION /data ext4 rw 0 1" | sudo tee -a /etc/fstab >/dev/null
#     sudo mount -o rw "$PARTITION" /data
#   else
#     echo "$PARTITION is already mounted on /data."
#   fi
# }

# # Mount the partition
# mount_partition() {
#   # Check if $PARTITION exists
#   if [ ! -b "$PARTITION" ]; then
#     echo "Error: $PARTITION does not exist."
#     return 1
#   fi

#   # Create folder /data if not exist
#   if [ ! -d "/data" ]; then
#     echo "Creating /data directory..."
#     sudo mkdir -p /data
#   fi

#   # Format the disk (if not already formatted)
#   if ! blkid "$PARTITION" >/dev/null 2>&1; then
#     echo "Formatting $PARTITION..."
#     sudo mkfs.ext4 "$PARTITION"
#   fi
# }

# main() {
#   # Mount the partition
#   mount_partition
#   # Add to /etc/fstab if not already present and Mount the disk
#   update_fstab
# }

# main