# Remove all docker container
bash /usr/local/bin/k3s-uninstall.sh
docker stop $(docker ps -q)
docker rm $(docker ps -a -q)
docker rmi -f $(docker images -q)

sudo apt-get purge docker.io docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin docker-ce-rootless-extras

# Remove file and folder
sudo rm -rf /var/lib/docker
sudo rm -rf /var/lib/containerd
sudo rm -rf /home/<USER>/.device_id
sudo rm -rf /home/<USER>/.kube/kubeconfig

systemctl stop worker-recording.service
systemctl disable worker-recording.service
sudo rm -rf /etc/systemd/system/worker-recording.service

systemctl stop worker-ota.service
systemctl disable worker-ota.service
sudo rm -rf /etc/systemd/system/worker-ota.service

systemctl stop worker-ap-setting.service
systemctl disable worker-ap-setting.service
sudo rm -rf /etc/systemd/system/worker-ap-setting.service

systemctl stop mount-data-folder.service
systemctl disable mount-data-folder.service
sudo rm -rf /etc/systemd/system/mount-data-folder.service