#!/bin/bash

# if /dev/sda exists, use it, otherwise use /dev/nvme0n1
if [ -b "/dev/sda" ]; then
    DISK="/dev/sda"
    PARTITION="${DISK}1"
else
    DISK="/dev/nvme0n1"
    PARTITION="${DISK}p1"
fi

# Unmount the partition
sudo umount $PARTITION 2>/dev/null

# Delete all partitions
delete_all_partitions() {
    echo "Deleting all partitions on $DISK..."
    sudo parted -s $DISK mklabel gpt
}

# Create a new partition
create_new_partition() {
    echo "Creating a new partition on $DISK..."
    sudo parted -s $DISK mkpart primary ext4 0% 100%
    sleep 5  # Wait for partition table to update
}

# Format the partition
format_partition() {
    echo "Formatting $PARTITION as ext4..."
    sudo mkfs.ext4 $PARTITION
}

# Main function
main() {
    delete_all_partitions
    create_new_partition
    format_partition
}

main
