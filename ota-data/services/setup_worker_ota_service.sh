#!/bin/sh

# Define the content of the systemd service unit file
SERVICE_FILE="/etc/systemd/system/worker-ota.service"
SERVICE_CONTENT="[Unit]
Description=worker-ota
After=network.target

[Service]
Environment="PATH=/usr/bin:/bin:/usr/sbin:/sbin"
WorkingDirectory=/home/<USER>
ExecStart=/home/<USER>/worker-ota worker
KillMode=process
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=what
Restart=on-failure
RestartSec=10

[Install]
WantedBy=multi-user.target
"

exec_cmd_nobail() {	
  echo "+ $2 bash -c \"$1\""	
  $2 bash -c "$1"	
}

print_status() {	
  echo "## $1"	
}

create_worker_ota_service() {
  # Echo the content into the systemd service file
  echo "$SERVICE_CONTENT" | sudo tee "$SERVICE_FILE" > /dev/null

  # Enable deamond service
  exec_cmd_nobail "systemctl enable worker-ota.service"

  # Start daemond service
  exec_cmd_nobail "systemctl start worker-ota.service"
}

echo "Create worker-ota..."
create_worker_ota_service
