# #!/bin/sh

# # Define the content of the systemd service unit file
# SETUP_SCRIPT="/etc/ota-data/utils/setup-ap.sh"
# SERVICE_FILE="/etc/systemd/system/worker-ap-setting.service"
# SERVICE_CONTENT="[Unit]
# Description=Setup AP mode
# After=multi-user.target

# [Service]
# Type=oneshot
# Environment="PATH=/usr/bin:/bin:/usr/sbin:/sbin"
# ExecStart=$SETUP_SCRIPT
# RemainAfterExit=yes
# StandardOutput=syslog
# StandardError=syslog
# SyslogIdentifier=worker-ap-setting

# [Install]
# WantedBy=multi-user.target
# "

# exec_cmd_nobail() {	
#   echo "+ $2 bash -c \"$1\""	
#   $2 bash -c "$1"	
# }

# print_status() {	
#   echo "## $1"	
# }

# run_service() {
  
#   exec_cmd_nobail "chmod +x $SETUP_SCRIPT" "sudo"
    
#   # Echo the content into the systemd service file
#   echo "$SERVICE_CONTENT" | sudo tee "$SERVICE_FILE" > /dev/null

#   # Enable deamond service
#   exec_cmd_nobail "systemctl enable worker-ap-setting.service"

#   # Start daemond service
#   exec_cmd_nobail "systemctl start worker-ap-setting.service"
# }

# echo "Run AP setting ..."
# run_service
