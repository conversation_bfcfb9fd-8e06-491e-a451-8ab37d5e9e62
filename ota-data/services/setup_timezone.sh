# #!/bin/sh

# # Define the content of the systemd service unit file
# SERVICE_FILE="/etc/systemd/system/setup-timezone.service"
# SERVICE_CONTENT="[Unit]
# Description=Set timezone to Asia/Ho_Chi_Minh on boot
# After=network.target

# [Service]
# Type=oneshot
# ExecStart=/usr/bin/timedatectl set-timezone Asia/Singapore
# RemainAfterExit=yes

# [Install]
# WantedBy=multi-user.target
# "

# exec_cmd_nobail() {	
#   echo "+ $2 bash -c \"$1\""	
#   $2 bash -c "$1"	
# }

# print_status() {	
#   echo "## $1"	
# }

# create_set_timezone_service() {
#   # Echo the content into the systemd service file
#   echo "$SERVICE_CONTENT" | sudo tee "$SERVICE_FILE" > /dev/null

#   # Enable deamond service
#   exec_cmd_nobail "systemctl enable setup-timezone.service"

#   # Start daemond service
#   exec_cmd_nobail "systemctl start setup-timezone.service"
# }

# echo "Create setup-timezone..."
# create_set_timezone_service
