# The upgrade jetpack version
# file="/etc/nv_tegra_release"

# Extract the release value
# release=$(grep -oP '(?<=# R)\d+' "$file")

# Extract the revision value
# revision=$(grep -oP '(?<=REVISION: )[\d\.]+' "$file")

# Download OTA tools
# ota_tool_file=/etc/ota-data/ota_tools_R${release}.${revision}_aarch64.tbz2
ota_tool_file=/etc/ota-data/ota_tools_aarch64.tbz2

# Create a directory to hold files that were generated in the OTA update process
export WORKDIR=$(pwd)/ota_update_workdir
sudo rm -rf $WORKDIR
mkdir ota_update_workdir

# Unpack ota_tools_<rel>_aarch64.tbz2 into the ${WORKDIR} directory.
echo "--> Unpack OTA Tools"
sudo tar xpf $ota_tool_file -C ${WORKDIR}

# Unpack the OTA payload package and prepare to start OTA
cd ${WORKDIR}/Linux_for_Tegra/tools/ota_tools/version_upgrade
sudo apt-get install efibootmgr nvme-cli -y
sudo ROOTFS_AB=1 ./nv_ota_start.sh $1

# reboot
echo "--> Reboot"
sudo reboot