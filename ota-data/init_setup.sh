#!/bin/sh

# Function to print usage
usage() {
    echo "Usage: $0 --env {dev|staging|prod} --version {VERSION}"
    exit 1
}

# Check if the correct number of arguments is provided
if [[ "$1" != "--env" || "$3" != "--version" ]]; then
    usage
fi

# Get environment and version value
ENV="$2"
VERSION="$4"

echo "Starting setup..."

################################
# Init partition
# This script will delete all partitions on the disk, create a new partition, format it as ext4, and mount it to /data.
################################
bash /etc/ota-data/utils/init_partition.sh


################################
# Generate the file worker_ota_config.yaml
# This script will generate the file worker_ota_config.yaml in /etc/ota-data/ with the following content
# This script will be used to configure the worker-ota service.
################################
CONFIG_FILE="/etc/ota-data/worker_ota_config.yaml"

# Set cloudUrl based on environment
case "$ENV" in
    dev)
        CLOUD_URL="https://api.mve2-dev.knizsoft.com/"
        ;;
    staging)
        CLOUD_URL="https://api.mve2-staging.knizsoft.com/"
        ;;
    prod)
        CLOUD_URL="https://api.mve2-prod.knovel.org/"
        ;;
    *)
        echo "Invalid environment: $ENV"
        usage
        ;;
esac

# Generate the YAML config file
cat <<EOF > "$CONFIG_FILE"
debug: true
ota:
  deviceType: "Jetson AGX Orin"
  cloudUrl: "$CLOUD_URL"
  timeSyncFirmware: 10
  pathKubeConfig: "/home/<USER>/.kube/kubeconfig"
  pathInitCmd: "/home/<USER>/setup.sh"
EOF
echo "Config file '$CONFIG_FILE' created successfully with environment: $ENV"



################################
# Update /etc/user_release_version
# The OTA Version
################################
USER_RELEASE_FILE="/etc/user_release_version"
if [[ -w "$USER_RELEASE_FILE" || ! -f "$USER_RELEASE_FILE" ]]; then
    echo "# User release: $VERSION" | sudo tee "$USER_RELEASE_FILE" > /dev/null
    echo "Updated $USER_RELEASE_FILE with version: $VERSION"
else
    echo "Error: No write permission for $USER_RELEASE_FILE"
    exit 1
fi


################################
# Install requirement
################################
bash /etc/ota-data/utils/install_requirement.sh


################################
# Clean data before backup
################################
rm -rf /home/<USER>/setup.sh
sudo rm -rf /home/<USER>/.kube
mkdir -p /home/<USER>/.kube
chmod 777 /home/<USER>/.kube
sudo rm -rf /home/<USER>/.device_id

echo "Setup completed successfully!"