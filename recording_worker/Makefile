.PHONY: os build-local build-deploy test vendor tidy

OSFLAG :=
GOARCH :=
VERSION?="1.0.0"
COMMIT?=$(shell git rev-parse --short HEAD)
DATE := $(shell date -u +'%Y-%m-%dT%H:%M:%SZ')

ifndef GO_BIN:
GO_BIN := go
endif

UNAME_S := $(shell uname -s)
ifeq ($(UNAME_S),Linux)
	OSFLAG = "linux"
	GOARCH = "amd64"
endif
ifeq ($(UNAME_S),Darwin)
	OSFLAG = "darwin"
	GOARCH = "arm64"
endif

os:
	@echo ${OSFLAG}

build-local:
	GO111MODULE=on CGO_ENABLED=0 GOOS=$(OSFLAG) GOARCH=$(GOARCH) $(GO_BIN) build -ldflags "-X main.VERSION=$(VERSION) -X main.COMMIT=$(COMMIT) -X main.DATE=$(DATE) -w -s" -v -o worker-recording main.go
build-deploy:
	GO111MODULE=on CGO_ENABLED=0 GOOS=linux GOARCH=arm64 $(GO_BIN) build -ldflags "-X main.VERSION=$(VERSION) -X main.COMMIT=$(COMMIT) -X main.DATE=$(DATE) -w -s" -v -o worker-recording main.go

test:
	@$(GO_BIN) mod download
	@$(GO_BIN) test ./... -coverprofile=coverage.out
	@$(GO_BIN) tool cover -html=coverage.out -o coverage.html
	@rm -rf coverage.out

vendor:
	@$(GO_BIN) mod vendor
tidy:
	@$(GO_BIN) mod tidy