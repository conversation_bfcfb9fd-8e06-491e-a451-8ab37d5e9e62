package main

import (
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"syscall"
	"time"
)

const (
	storageDir   = "/data/cluster_data/data/"
	containerDir = "/mnt/data/"
)

type processEntry struct {
	Param   recordingParam
	Command *exec.Cmd
}

type recordingParam struct {
	Uri         string `json:"uri"`
	CommandPath string `json:"command_path"`
	StoragePath string `json:"storage_path"`
	LastChanged int64  `json:"last_changed"`
}

func (param recordingParam) GenerateCommand() string {
	cmd := ""
	isRtsp := true
	if strings.Contains(param.Uri, "/dev/video") {
		isRtsp = false
	}

	if isRtsp {
		cmd = "ffmpeg -hide_banner -y -loglevel error -rtsp_transport tcp -use_wallclock_as_timestamps 1 -i " + param.Uri + " -vcodec copy -acodec copy -f segment -reset_timestamps 1 -segment_time 1800 -segment_format mkv -segment_atclocktime 1 -strftime 1 " + param.StoragePath + "/record%Y_%m_%dT_%H_%M_%S.mkv"
	} else {
		timestamp := fmt.Sprintf("%d", time.Now().Unix())
		cmd = "gst-launch-1.0 v4l2src device=" + param.Uri + " ! nvvidconv ! nvv4l2h264enc bitrate=1378000 ! h264parse ! splitmuxsink location=" + param.StoragePath + "/recorded_" + timestamp + "_%d.mp4 max-size-time=1800000000000 -e"
	}
	return cmd
}

func readFile(filePath string) (recordingParam, error) {
	// Read the JSON file to get recording parameters
	content, err := os.ReadFile(filePath)
	if err != nil {
		fmt.Printf("Error reading command file %s: %v\n", filePath, err)
		return recordingParam{}, err
	}

	var param recordingParam
	if err := json.Unmarshal(content, &param); err != nil {
		fmt.Printf("Error parsing JSON in file %s: %v\n", filePath, err)
		return recordingParam{}, err
	}

	// replace command path, storage path to match storageDir:
	// example: /mnt/data/record-command/cam-id.json -> /data/cluster-data/data/record-command/cam-id.json
	param.CommandPath = strings.Replace(param.CommandPath, containerDir, storageDir, 1)
	param.StoragePath = strings.Replace(param.StoragePath, containerDir, storageDir, 1)

	fmt.Println("param: ", param)

	return param, nil
}

func main() {
	recordCommandDir := filepath.Join(storageDir, "record-command")
	currentProcs := make(map[string]processEntry)
	timeToLive := int64(300) // 5 minutes

	fmt.Println("Start recording worker")

	for {
		time.Sleep(5 * time.Second)

		// Scan the directory for command files
		fmt.Println("scanning: ", recordCommandDir)
		files, err := os.ReadDir(recordCommandDir)
		if err != nil {
			fmt.Printf("Error reading command directory: %v\n", err)
			continue
		}

		// Start new recording processes for new files
		for _, file := range files {
			if filepath.Ext(file.Name()) == ".json" {
				camID := strings.TrimSuffix(file.Name(), ".json")

				// If there's no process for this camera, start one
				if _, exists := currentProcs[camID]; !exists {
					filePath := filepath.Join(recordCommandDir, file.Name())

					// Read the JSON file to get recording parameters
					param, err := readFile(filePath)
					if err != nil {
						continue
					}

					// check if the last changed time exceeds the timeout
					if time.Now().Unix()-param.LastChanged > timeToLive {
						fmt.Printf("Recording for camera %s exceeds timeout: %d\n", camID, param.LastChanged)
						continue
					}

					// Build the FFmpeg command based on parameters
					cmd := exec.Command("sh", "-c", param.GenerateCommand())

					// print the command string
					fmt.Println("cmd: ", cmd.String())

					cmd.SysProcAttr = &syscall.SysProcAttr{Setpgid: true} // Create a new process group for the command
					// Start the recording process
					if err := cmd.Start(); err == nil {
						entry := processEntry{param, cmd}

						currentProcs[camID] = entry
						fmt.Printf("Started recording for camera %s\n", camID)

						// Goroutine to clean up after process finishes
						go func(camID string, cmd *exec.Cmd) {
							defer delete(currentProcs, camID)
							if err := cmd.Wait(); err != nil {
								fmt.Printf("Recording error for camera %s: %v\n", camID, err)
							}
						}(camID, cmd)
					} else {
						fmt.Printf("Failed to start recording for camera %s: %v\n", camID, err)
					}
				}
			}
		}

		// Stop any processes for cameras if command file not exist or exceeds the timeout
		for camID, entry := range currentProcs {
			filePath := entry.Param.CommandPath
			isStop := false

			if _, err := os.Stat(filePath); os.IsNotExist(err) {
				fmt.Printf("Stopping recording for camera %s: command file not found\n", camID)
				isStop = true
			} else {
				// Read the JSON file to get recording parameters
				param, err := readFile(filePath)
				if err != nil {
					continue
				}

				// check if the last changed time exceeds the timeout
				if time.Now().Unix()-param.LastChanged > timeToLive {
					fmt.Printf("Stopping camera %s exceeds timeout: %d\n", camID, param.LastChanged)
					isStop = true
				}
			}

			// Stop the process
			if isStop {
				fmt.Printf("Stopping recording for camera %s\n", camID)
				syscall.Kill(-entry.Command.Process.Pid, syscall.SIGKILL)
			}
		}
	}
}
